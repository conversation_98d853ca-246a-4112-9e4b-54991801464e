import Fastify, { FastifyInstance } from 'fastify';
import fastifyFormBody from '@fastify/formbody';
import fastifyWs from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import fastifyStatic from '@fastify/static';
import fastifyCompress from '@fastify/compress';
import fastifyRateLimit from '@fastify/rate-limit';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import path from 'path';

// Import modular components
import { initializeGeminiClient } from './src/gemini/client';
import { voiceManager } from './src/gemini/voice-manager';
import { modelManager } from './src/gemini/model-manager';
import { ContextManager } from './src/session/context-manager';
import { SessionManager } from './src/session/session-manager';
import { ConnectionHealthMonitor } from './src/session/health-monitor';
import { SessionRecoveryManager } from './src/session/recovery-manager';
import { SessionSummaryManager } from './src/session/summary-manager';
import { SessionLifecycleManager } from './src/session/lifecycle-manager';
import { ScriptManager } from './src/scripts/script-manager';
import { AudioProcessor } from './src/audio/audio-processor';
import { TranscriptionManager } from './src/audio/transcription-manager';
import { globalHeartbeatManager } from './src/websocket/heartbeat-manager';
import { registerWebSocketHandlers } from './src/websocket/handlers';
import { registerApiRoutes } from './src/api/routes';
import { registerManagementRoutes } from './src/api/management';
import { registerTestingRoutes } from './src/api/testing';
import { validateSupabaseAuth } from './src/middleware/auth-simple';
import { logger } from './src/utils/logger';
import { timerManager } from './src/utils/timer-manager';

// Import types
import type { ConnectionData, AppConfig } from './src/types/global';

// Get directory paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load configuration system
import { config, validateConfig } from './src/config/config';

// Validate configuration on startup
validateConfig();

// Global error handlers for production safety
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('🚨 Unhandled Promise Rejection', { promise, reason, stack: reason?.stack });
    
    // Log additional context for debugging
    logger.error('🚨 Process info', {
        pid: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime()
    });
    
    // In production, might want to restart after cleanup
    if (process.env.NODE_ENV === 'production') {
        logger.error('🚨 Production environment: initiating graceful shutdown');
        process.exit(1);
    }
});

process.on('uncaughtException', (error: Error) => {
    logger.error('🚨 Uncaught Exception', { error });
    logger.error('🚨 Process info', {
        pid: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime()
    });
    
    // Uncaught exceptions are always fatal
    logger.error('🚨 Fatal error: process will exit');
    process.exit(1);
});

process.on('warning', (warning: any) => {
    logger.warn('⚠️ Node.js Warning', { name: warning.name, message: warning.message, stack: warning.stack });
});

// Get default voice and model from configuration
const GEMINI_DEFAULT_VOICE: string = config.ai.gemini.defaultVoice;
const GEMINI_DEFAULT_MODEL: string = config.ai.gemini.defaultModel;

// Get other configuration constants
const SUMMARY_GENERATION_PROMPT: string = config.prompts.summaryGeneration;
const AI_PREPARE_MESSAGE: string = config.prompts.aiPrepareOutbound;
const TWILIO_ACCOUNT_SID: string = config.auth.twilio.accountSid;
const TWILIO_AUTH_TOKEN: string = config.auth.twilio.authToken;
const PUBLIC_URL: string = config.server.publicUrl;
const PORT: number = config.server.port;

logger.info('🚀 Twilio Gemini Live API Server starting...');
logger.info(`📝 Using Gemini API Key: ${config.auth.gemini.apiKey ? 'SET ✅' : 'NOT SET ❌'}`);
logger.info(`📞 Twilio Config: ${config.auth.twilio.accountSid ? 'SET ✅' : 'NOT SET ❌'}`);
logger.info(`🎤 Deepgram API Key: ${config.auth.deepgram.apiKey ? 'SET ✅' : 'NOT SET ❌'}`);
logger.info(`🔗 Public URL: ${config.server.publicUrl || 'NOT SET'}`);
logger.info(`🤖 Default Model: ${GEMINI_DEFAULT_MODEL}`);
logger.info(`🎵 Default Voice: ${GEMINI_DEFAULT_VOICE}`);

// Initialize core components
const geminiClient = initializeGeminiClient(config.auth.gemini.apiKey);
const contextManager = new ContextManager();

// Bounded Map implementation for memory safety
class BoundedMap<K, V> extends Map<K, V> {
    private maxSize: number;
    private lifecycleManagerRef: any = null;

    constructor(maxSize: number = 1000) {
        super();
        this.maxSize = maxSize;
    }

    setLifecycleManager(lifecycleManagerRef: any): void {
        this.lifecycleManagerRef = lifecycleManagerRef;
    }

    set(key: K, value: V): this {
        // Remove oldest entries if we're at capacity
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            const oldValue = this.get(firstKey as K);
            
            // Properly cleanup the connection before removing
            if (oldValue && typeof oldValue === 'object') {
                logger.warn(`⚠️ BoundedMap: Forcing cleanup of oldest entry ${String(firstKey)} (capacity: ${this.maxSize})`);
                
                // If it's a connection object, close it
                const connectionData = oldValue as any;
                if (connectionData?.twilioWs && typeof connectionData.twilioWs.close === 'function') {
                    try {
                        connectionData.twilioWs.close();
                    } catch (e) {
                        logger.error(`Error closing WebSocket for ${String(firstKey)}:`, e as Error);
                    }
                }
                
                // Trigger lifecycle cleanup if possible
                if (this.lifecycleManagerRef && typeof this.lifecycleManagerRef.endSession === 'function') {
                    this.lifecycleManagerRef.endSession(firstKey, oldValue, 'capacity_limit').catch((err: any) => {
                        logger.error(`Error ending session ${String(firstKey)}:`, err);
                    });
                }
            }
            
            this.delete(firstKey as K);
        }
        return super.set(key, value);
    }
}

const activeConnections = new BoundedMap<string, ConnectionData>(500); // Limit to 500 concurrent connections
// Pass activeConnections reference
const sessionManager = new SessionManager(contextManager, geminiClient, activeConnections);
const healthMonitor = new ConnectionHealthMonitor();
const summaryManager = new SessionSummaryManager();
const lifecycleManager = new SessionLifecycleManager(contextManager, healthMonitor, summaryManager);
// Set the lifecycle manager reference for cleanup
activeConnections.setLifecycleManager(lifecycleManager);
const recoveryManager = new SessionRecoveryManager(contextManager, healthMonitor, geminiClient, sessionManager);
const scriptManager = new ScriptManager();
const audioProcessor = new AudioProcessor();
const transcriptionManager = new TranscriptionManager();

// Initialize Fastify server
const fastify: FastifyInstance = Fastify({ logger: false });

// Register plugins
await fastify.register(fastifyWs);
await fastify.register(fastifyCors, {
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://twilio-gemini.verduona.com', 'https://gemini-api.verduona.com']
        : ['http://localhost:3000', 'http://localhost:3011', 'http://localhost:3101'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});
await fastify.register(fastifyFormBody);

// Register authentication middleware
fastify.addHook('preHandler', validateSupabaseAuth);

// Add security headers middleware
fastify.addHook('onSend', async (request, reply, payload) => {
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    if (process.env.NODE_ENV === 'production') {
        reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        reply.header('Content-Security-Policy', 
            "default-src 'self'; " +
            "connect-src 'self' wss: ws:; " +
            "script-src 'self' 'unsafe-inline'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "media-src 'self' blob:; " +
            "font-src 'self';"
        );
    }
    
    return payload;
});

// Register compression for better performance
await fastify.register(fastifyCompress, {
    global: true,
    threshold: 1024 // Only compress responses larger than 1KB
});

// Register rate limiting for security
await fastify.register(fastifyRateLimit, {
    max: 100, // Maximum 100 requests
    timeWindow: '1 minute', // Per minute
    skipOnError: true // Don't count failed requests
});

// Register static file serving for management interfaces
if (!fastify.hasReplyDecorator || !fastify.hasReplyDecorator('sendFile')) {
    await fastify.register(fastifyStatic, {
        root: path.join(__dirname, 'static'),
        prefix: '/static/'
    });
} else {
    logger.warn('⚠️ Static routes already registered - skipping duplicate registration');
}

// Dependencies object for modules
const dependencies = {
    config,
    geminiClient,
    contextManager,
    sessionManager,
    healthMonitor,
    summaryManager,
    lifecycleManager,
    recoveryManager,
    scriptManager,
    audioProcessor,
    transcriptionManager,
    activeConnections,
    voiceManager,
    modelManager,
    GEMINI_DEFAULT_VOICE,
    GEMINI_DEFAULT_MODEL,
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL
};

// Register API routes first so decorators like getNextCallConfig are available
logger.info('🔧 Calling registerApiRoutes...');
try {
    registerApiRoutes(fastify, dependencies);
    logger.info('✅ API routes registration completed');
} catch (error) {
    logger.error('❌ Error in registerApiRoutes', { error: error as Error });
    throw error;
}

// Expose getNextCallConfig to WebSocket handlers
if (typeof fastify.getNextCallConfig === 'function') {
    (dependencies as any).getNextCallConfig = fastify.getNextCallConfig;
}

// Register WebSocket handlers after API routes
logger.info('🔧 Registering WebSocket handlers...');
registerWebSocketHandlers(fastify, dependencies);
logger.info('✅ WebSocket handlers registered');

// Register management routes
logger.info('🔧 Registering management routes...');
registerManagementRoutes(fastify, dependencies);
logger.info('✅ Management routes registered');

// Register testing routes
logger.info('🔧 Registering testing routes...');
registerTestingRoutes(fastify, dependencies);
logger.info('✅ Testing routes registered');

// === MANAGEMENT INTERFACE ROUTES ===

// Incoming call management interface
fastify.get('/incoming', async (_request, reply) => {
    try {
        const html = await import('fs/promises').then(fs =>
            fs.readFile(path.join(__dirname, 'incoming-manager.html'), 'utf8')
        );
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        logger.error('❌ Error loading incoming call management interface', { error: error as Error });
        return reply.code(500).send({ error: 'Failed to load incoming call management interface' });
    }
});

// Outbound scripts management interface
fastify.get('/outbound-scripts', async (_request, reply) => {
    try {
        const html = await import('fs/promises').then(fs =>
            fs.readFile(path.join(__dirname, 'outbound-scripts-manager.html'), 'utf8')
        );
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        logger.error('❌ Error loading outbound scripts management interface', { error: error as Error });
        return reply.code(500).send({ error: 'Failed to load outbound scripts management interface' });
    }
});

// Redirect route for backward compatibility
fastify.get('/scripts', async (_request, reply) => {
    return reply.redirect('/outbound-scripts');
});

// Preload campaign scripts into cache
await scriptManager.preloadScripts();

// Cleanup old contexts and sessions periodically using timer manager
timerManager.setInterval('global_cleanup', () => {
    contextManager.cleanupOldContexts();
    lifecycleManager.cleanup();
}, 300000); // Every 5 minutes

// Start the server
const start = async (): Promise<void> => {
    try {
        await fastify.listen({
            port: PORT,
            host: '0.0.0.0'
        });
        logger.info(`🚀 Server listening on port ${PORT}`);
        logger.info(`🔗 WebSocket endpoint: ws://localhost:${PORT}/media-stream`);
        logger.info(`🧪 Local audio testing: ws://localhost:${PORT}/local-audio-session`);
        logger.info(`🏥 Health check: http://localhost:${PORT}/health`);
        logger.info(`📊 API documentation: http://localhost:${PORT}/`);

        // Log voice and model configuration
        modelManager.logConfiguration();
        const voiceInfo = voiceManager.getVoiceInfo(GEMINI_DEFAULT_VOICE)?.characteristics || 'unknown';
        logger.info(`🎤 Default Voice: ${GEMINI_DEFAULT_VOICE} (${voiceInfo})`);
        logger.info(`🎵 Available Voices: ${Object.keys(voiceManager.getAvailableVoices()).join(', ')}`);
        logger.info(`🔧 Voice Selection: ${voiceManager.isVoiceSelectionEnabled() ? 'Enabled' : 'Disabled'}`);

        // Graceful shutdown handlers
        const gracefulShutdown = async (signal: string): Promise<void> => {
            logger.info(`\n🛑 Received ${signal}, initiating graceful shutdown...`);

            try {
                await performShutdownSequence();
                logger.info('✅ Graceful shutdown completed');
                process.exit(0);
            } catch (error) {
                logger.error('❌ Error during graceful shutdown', { error: error as Error });
                process.exit(1);
            }
        };

        const performShutdownSequence = async (): Promise<void> => {
            // Stop accepting new connections
            logger.info('🔒 Stopping server from accepting new connections...');
            await fastify.close();

            // Clean up all manager resources
            logger.info('🧹 Cleaning up session management resources...');

            // Stop all WebSocket heartbeats
            globalHeartbeatManager.stopAllHeartbeats();

            // Clean up managers
            await cleanupManagers();

            // Clean up all active sessions
            await cleanupActiveSessions();

            // Clean up periodic intervals using timer manager
            logger.info('🗑️ Clearing periodic intervals...');
            timerManager.clearAll();

            // Clean up all Maps and Sets
            logger.info('🗑️ Clearing data structures...');
            activeConnections.clear();
        };

        const cleanupManagers = async (): Promise<void> => {
            const managers = [
                { obj: healthMonitor, method: 'stopHealthChecks', name: 'healthMonitor' },
                { obj: contextManager, method: 'clearAllContexts', name: 'contextManager' },
                { obj: recoveryManager, method: 'cleanup', name: 'recoveryManager' },
                { obj: summaryManager, method: 'cleanup', name: 'summaryManager' },
                { obj: transcriptionManager, method: 'cleanup', name: 'transcriptionManager' }
            ];

            for (const { obj, method, name } of managers) {
                if (obj && (obj as any)[method]) {
                    try {
                        (obj as any)[method]();
                    } catch (error) {
                        logger.warn(`⚠️ Error cleaning up ${name}`, { error });
                    }
                }
            }

            // Session manager specific cleanup
            if (sessionManager) {
                for (const [sessionId] of (sessionManager as any).sessionMetrics) {
                    sessionManager.cleanupSession(sessionId);
                }
            }
        };

        const cleanupActiveSessions = async (): Promise<void> => {
            if (!lifecycleManager) {
                return;
            }

            const activeSessions = lifecycleManager.getActiveSessions();
            logger.info(`🔚 Ending ${activeSessions.length} active sessions...`);

            for (const sessionId of activeSessions) {
                try {
                    const connectionData = activeConnections.get(sessionId);
                    await (lifecycleManager as any).forceEndSession(
                        sessionId, connectionData, 'server_shutdown'
                    );
                } catch (error) {
                    logger.warn(`⚠️ Error ending session ${sessionId}`, { error: error as Error });
                }
            }
        };

        // Register shutdown handlers
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

        // Handle PM2 graceful reload
        process.on('message', (msg: any) => {
            if (msg === 'shutdown') {
                gracefulShutdown('PM2_SHUTDOWN');
            }
        });

        logger.info('✅ Server is ready to handle calls!');
    } catch (err) {
        logger.error('❌ Error starting server', { error: err as Error });
        process.exit(1);
    }
};

start();
