import crypto from 'crypto';
import { config, getConfigValue } from '../config/config';
import { FastifyRequest, FastifyReply } from 'fastify';

interface TwilioWebhookRequest extends FastifyRequest {
  // protocol is inherited from FastifyRequest
  // Additional Twilio-specific properties can be added here
}

/**
 * Validate Twilio webhook signatures to ensure requests are from Twilio
 */
export class TwilioWebhookValidator {
  private authToken: string;
  private skipValidation: boolean;

  constructor() {
    this.authToken = getConfigValue('twilio.authToken', '');
    // Only allow skipping validation in test environment for security
    this.skipValidation = getConfigValue('twilio.skipWebhookValidation', false) && process.env.NODE_ENV === 'test';
  }

  /**
   * Validate a Twilio webhook request
   * @param signature - The X-Twilio-Signature header value
   * @param url - The full URL of the webhook endpoint
   * @param params - The request body parameters
   * @returns Whether the signature is valid
   */
  validateRequest(
    signature: string | undefined, 
    url: string, 
    params: Record<string, any> | undefined
  ): boolean {
    // Only skip validation in test environment for security
    if (this.skipValidation && process.env.NODE_ENV === 'test') {
      console.warn('⚠️ Twilio webhook signature validation is disabled for testing');
      return true;
    }

    // Always validate in production and development for security
    if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'development') {
      console.log('🔒 Enforcing Twilio webhook signature validation');
    }

    if (!signature || !url || !this.authToken) {
      console.error('❌ Missing required parameters for Twilio signature validation');
      return false;
    }

    try {
      // Sort the POST parameters alphabetically by key
      const sortedParams = Object.keys(params || {})
        .sort()
        .reduce((acc: Record<string, any>, key) => {
          acc[key] = params![key];
          return acc;
        }, {});

      // Build the validation string
      let validationString = url;
      for (const [key, value] of Object.entries(sortedParams)) {
        validationString += key + (value || '');
      }

      // Calculate the expected signature
      const expectedSignature = crypto
        .createHmac('sha1', this.authToken)
        .update(validationString)
        .digest('base64');

      // Compare signatures
      const isValid = signature === expectedSignature;
      
      if (!isValid) {
        console.error('❌ Twilio webhook signature validation failed');
        console.error('Expected:', expectedSignature);
        console.error('Received:', signature);
      }

      return isValid;
    } catch (error) {
      console.error('❌ Error validating Twilio webhook signature:', error);
      return false;
    }
  }

  /**
   * Express/Fastify middleware for validating Twilio webhooks
   */
  middleware() {
    return async (request: TwilioWebhookRequest, reply: FastifyReply): Promise<void> => {
      // Skip validation for non-Twilio endpoints
      const twilioEndpoints = ['/incoming-call', '/call-status', '/voice-status'];
      if (!twilioEndpoints.includes(request.url)) {
        return;
      }

      const signature = request.headers['x-twilio-signature'] as string | undefined;
      const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
      
      if (!this.validateRequest(signature, fullUrl, request.body as Record<string, any>)) {
        reply.code(403).send({ error: 'Invalid Twilio signature' });
        return;
      }
    };
  }
}

// Export singleton instance
export const twilioValidator = new TwilioWebhookValidator();

/**
 * Validate a Twilio webhook request
 * @param request - The HTTP request object
 * @returns Whether the request is valid
 */
export function validateTwilioWebhook(request: TwilioWebhookRequest): boolean {
  const signature = request.headers['x-twilio-signature'] as string | undefined;
  const fullUrl = `${request.protocol}://${request.headers.host}${request.url}`;
  
  return twilioValidator.validateRequest(signature, fullUrl, request.body as Record<string, any>);
}