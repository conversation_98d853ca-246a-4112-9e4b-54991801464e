// Main API routes for the Twilio Gemini service
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { SecurityUtils } from '../middleware/security-utils';
import { AudioProcessor } from '../audio/audio-processor';
import { TranscriptionManager } from '../audio/transcription-manager';
import { loadCampaignScript } from '../scripts/campaign-loader';
import twilio from 'twilio';
import { config } from '../config/config';
import { registerWebhookRoutes } from './webhook-routes';

import {
    Dependencies,
    CallConfig,
    CallStatusBody,
    ConfigureCallBody,
    EndSessionParams,
    SetVoiceBody,
    SetModelBody,
    AudioSettingsBody,
    TriggerRecoveryParams,
    CampaignScriptParams
} from '../types/api-types';

// Type definitions for routes not yet moved to shared types

interface IncomingCampaignParams {
    id: string;
}

interface ScriptIdParams {
    scriptId: string;
}

interface SessionIdParams {
    sessionId: string;
}

interface EndSessionBody {
    reason?: string;
}

interface UpdateSessionConfigBody {
    voice?: string;
    model?: string;
    aiInstructions?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}

interface MakeCallBody {
    to: string;
    from: string;
    task?: string;
    voice?: string;
    model?: string;
    targetName?: string;
    targetPhoneNumber?: string;
    outputLanguage?: string;
}

interface CallResultsParams {
    callSid: string;
}

interface ConfigureIncomingScenarioBody {
    scenarioId?: string;
    name?: string;
    language?: string;
    fromNumber?: string;
    voice?: string;
    model?: string;
    country?: string;
    script?: string;
    isActive?: boolean;
}

interface SelectScenarioBody {
    scenarioId: string;
}

export function registerApiRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    console.log('🚀 Starting API route registration...');
    console.log('🔍 [DEBUG] Dependencies received:', Object.keys(dependencies || {}));
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        recoveryManager,
        lifecycleManager,
        summaryManager,
        scriptManager,
        voiceManager,
        modelManager,
        GEMINI_DEFAULT_VOICE,
        GEMINI_DEFAULT_MODEL,
        SUMMARY_GENERATION_PROMPT
    } = dependencies;
    console.log('✅ Dependencies extracted successfully');
    console.log('🔍 [DEBUG] scriptManager extracted:', !!scriptManager, typeof scriptManager);

    // Register webhook routes
    registerWebhookRoutes(fastify, dependencies);

    // Configuration values
    const TWILIO_ACCOUNT_SID = config.auth.twilio.accountSid;
    const TWILIO_AUTH_TOKEN = config.auth.twilio.authToken;
    const PUBLIC_URL = config.server.publicUrl;

    // Root route - API information
    fastify.get('/', async (_request: FastifyRequest, _reply: FastifyReply) => {
        return {
            service: 'Twilio Gemini Live API',
            status: 'running',
            version: '2.0.0',
            activeConnections: activeConnections.size,
            endpoints: {
                health: '/health',
                websocket: '/media-stream',
                localAudio: '/local-audio-session',
                configure: '/configure-call',
                endSession: '/end-session/:callSid',
                voices: '/available-voices',
                models: '/available-models',
                setVoice: '/set-voice',
                setModel: '/set-model',
                audioSettings: '/audio-settings',
                connectionMetrics: '/api/connection-metrics',
                providerHealth: '/api/provider-health',
                incomingScenarios: '/api/incoming-scenarios',
                configureIncomingScenario: '/api/configure-incoming-scenario'
            }
        };
    });

    // Enhanced health check route
    fastify.get('/health', async (_request: FastifyRequest, _reply: FastifyReply) => {
        const contextStats = contextManager.getContextStats();
        const transcriptionManager = new TranscriptionManager();
        let transcriptionHealth;
        try {
            transcriptionHealth = await transcriptionManager.healthCheck();
        } catch (error) {
            console.error('Failed to get transcription health:', error);
            transcriptionHealth = { status: 'error', error: (error as Error).message };
        }

        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            activeConnections: activeConnections.size,
            contextStats,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            transcription: transcriptionHealth,
            healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
            nextCallConfig: await (async () => {
                const config = await (fastify as any).getNextCallConfig();
                return config ? {
                    hasInstructions: !!config.aiInstructions,
                    voice: config.voice,
                    model: config.model,
                    targetName: config.targetName
                } : null;
            })()
        };
    });


    // Configure next outbound call
    fastify.post<{ Body: ConfigureCallBody }>('/configure-call', async (request, _reply) => {
        try {
            // Validate request body exists
            if (!request.body) {
                return {
                    success: false,
                    error: 'Request body is required'
                };
            }
            
            const { aiInstructions, voice, model, targetName, targetPhoneNumber } = request.body;
            
            // Get current config from webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL,
                targetName: null,
                targetPhoneNumber: null,
                phoneNumber: '',
                mode: 'outbound'
            };
            
            const updatedConfig = {
                aiInstructions: aiInstructions || currentConfig.aiInstructions,
                voice: voice || GEMINI_DEFAULT_VOICE,
                model: model || GEMINI_DEFAULT_MODEL,
                targetName: targetName || null,
                targetPhoneNumber: targetPhoneNumber || null,
                phoneNumber: targetPhoneNumber || currentConfig.phoneNumber,
                mode: 'outbound'
            };

            // Update config in webhook module
            (fastify as any).setNextCallConfig(updatedConfig);

            console.log(`⚙️ Call configured for: ${updatedConfig.targetName || 'Unknown'}`);
            
            return { 
                success: true, 
                config: updatedConfig 
            };
        } catch (error) {
            console.error('❌ Error configuring call:', error);
            return { 
                success: false, 
                error: (error as Error).message 
            };
        }
    });

    // Get current call configuration
    fastify.get('/get-call-config', async (_request: FastifyRequest, _reply: FastifyReply) => {
        const currentConfig = await (fastify as any).getNextCallConfig();
        return {
            success: true,
            config: currentConfig
        };
    });

    // Manually end a session
    fastify.post<{ Params: EndSessionParams }>('/end-session/:callSid', async (request, _reply) => {
        try {
            const { callSid } = request.params;
            
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                // Generate summary if conversation exists
                if (connectionData.conversationLog.length > 0) {
                    await sessionManager.generateSummary(callSid, connectionData, SUMMARY_GENERATION_PROMPT);
                    // End session after summary timeout
                    setTimeout(() => {
                        endSessionHelper(callSid, { sessionManager, contextManager, activeConnections });
                    }, 30000);
                } else {
                    endSessionHelper(callSid, { sessionManager, contextManager, activeConnections });
                }
                
                return { success: true, message: 'Session ending initiated' };
            } else {
                return { success: false, message: 'Session not found' };
            }
        } catch (error) {
            console.error('❌ Error ending session:', error);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get available Gemini voices
    fastify.get('/available-voices', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            return {
                success: true,
                voices: voiceConfig.availableVoices,
                currentDefault: voiceConfig.defaultVoice,
                voiceMapping: voiceConfig.voiceMapping,
                voiceSelectionEnabled: voiceConfig.voiceSelectionEnabled,
                totalVoices: voiceConfig.totalVoices
            };
        } catch (error) {
            console.error('❌ Error getting available voices:', error);
            return {
                success: false,
                error: 'Failed to get available voices',
                message: (error as Error).message
            };
        }
    });

    // Voice Configuration Endpoint
    fastify.get('/api/voice-config', {
        schema: {
            response: {
                200: {
                    type: 'object',
                    properties: {
                        defaultVoice: { type: 'string' },
                        availableVoices: { type: 'object' },
                        voiceMapping: { type: 'object' },
                        voiceSelectionEnabled: { type: 'boolean' },
                        totalVoices: { type: 'number' },
                        voiceDescriptions: { type: 'object' }
                    }
                }
            }
        }
    }, async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            const envVoices = config?.ai?.gemini?.voices || {};
            
            // Parse voice descriptions from env
            const voiceDescriptions: Record<string, any> = {};
            if (envVoices && typeof envVoices === 'object') {
                Object.keys(envVoices).forEach(voiceName => {
                    const description = envVoices[voiceName];
                    voiceDescriptions[voiceName] = description;
                });
            }
            
            return reply.code(200).send({
                ...voiceConfig,
                voiceDescriptions
            });
        } catch (error) {
            console.error('❌ Error getting voice config:', error);
            return reply.code(500).send({ 
                error: 'Failed to get voice configuration',
                details: (error as Error).message 
            });
        }
    });

    // Get available Gemini models
    fastify.get('/available-models', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            const modelConfig = modelManager.getModelConfig();
            const availableModels = modelConfig.availableModels || {
                'gemini-2.5-flash-preview-native-audio-dialog': {
                    name: 'Gemini 2.5 Flash Preview Native Audio Dialog',
                    audioSupport: true
                },
                'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash Experimental', audioSupport: true }
            };
            
            return {
                success: true,
                availableModels: availableModels,
                defaultModel: modelConfig.defaultModel,
                currentModel: modelConfig.currentModel,
                modelSelectionEnabled: modelConfig.modelSelectionEnabled,
                totalModels: modelConfig.totalModels || Object.keys(availableModels).length,
                configurationSource: modelConfig.configurationSource
            };
        } catch (error) {
            console.error('❌ Error getting available models:', error);
            return {
                success: false,
                error: 'Failed to get available models',
                message: (error as Error).message
            };
        }
    });

    // Set default voice for next calls
    fastify.post<{ Body: SetVoiceBody }>('/set-voice', async (request, _reply) => {
        try {
            const { voice } = request.body;

            if (!voice) {
                return {
                    success: false,
                    error: 'Voice parameter is required'
                };
            }

            const validation = voiceManager.validateVoice(voice);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error,
                    suggestion: validation.suggestion,
                    availableVoices: validation.availableVoices,
                    voiceMapping: validation.voiceMapping
                };
            }

            const validVoice = validation.voice!;
            // Update config in webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {};
            (fastify as any).setNextCallConfig({ ...currentConfig, voice: validVoice });

            const voiceInfo = voiceManager.getVoiceInfo(validVoice);
            return {
                success: true,
                voice: validVoice,
                voiceInfo,
                mapped: validation.mapped || false,
                originalVoice: validation.originalVoice,
                message: `Voice set to: ${validVoice} (${voiceInfo?.characteristics || 'unknown'})`
            };
        } catch (error) {
            console.error('❌ Error setting voice:', error);
            return {
                success: false,
                error: 'Internal server error',
                message: (error as Error).message
            };
        }
    });

    // Set default model for next calls
    fastify.post<{ Body: SetModelBody }>('/set-model', async (request, _reply) => {
        try {
            const { model } = request.body;

            if (!model) {
                return {
                    success: false,
                    error: 'Model parameter is required'
                };
            }

            const validation = modelManager.validateModel(model);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error,
                    suggestion: validation.suggestion,
                    availableModels: validation.availableModels
                };
            }

            const validModel = validation.model!;
            // Update config in webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {};
            (fastify as any).setNextCallConfig({ ...currentConfig, model: validModel });

            const modelInfo = modelManager.getModelInfo(validModel);
            return {
                success: true,
                model: validModel,
                modelInfo,
                message: `Model set to: ${validModel} (${modelInfo?.name || 'unknown'})`
            };
        } catch (error) {
            console.error('❌ Error setting model:', error);
            return {
                success: false,
                error: 'Internal server error',
                message: (error as Error).message
            };
        }
    });

    // Get/Set audio settings
    fastify.get('/audio-settings', async (_request: FastifyRequest, _reply: FastifyReply) => {
        // Validate sessionManager and audioProcessor exist
        if (!sessionManager || !sessionManager.audioProcessor) {
            return {
                success: false,
                error: 'Audio processor not available'
            };
        }
        
        const audioSettings = sessionManager.audioProcessor.getAudioSettings();
        return {
            success: true,
            settings: audioSettings
        };
    });

    fastify.post<{ Body: AudioSettingsBody }>('/audio-settings', async (request, _reply) => {
        try {
            // Validate request body and sessionManager
            if (!request.body) {
                return {
                    success: false,
                    error: 'Request body is required'
                };
            }
            
            if (!sessionManager || !sessionManager.audioProcessor) {
                return {
                    success: false,
                    error: 'Audio processor not available'
                };
            }
            
            const newSettings = request.body;
            sessionManager.audioProcessor.updateAudioSettings(newSettings);
            
            return {
                success: true,
                settings: sessionManager.audioProcessor.getAudioSettings(),
                message: 'Audio settings updated'
            };
        } catch (error) {
            return {
                success: false,
                error: (error as Error).message
            };
        }
    });

    // Get connection metrics
    fastify.get('/api/connection-metrics', async (_request: FastifyRequest, _reply: FastifyReply) => {
        const metrics = {
            activeConnections: activeConnections.size,
            connectionDetails: [] as any[],
            contextStats: contextManager.getContextStats()
        };

        // Get details for each active connection
        for (const [sessionId, connectionData] of Array.from(activeConnections.entries())) {
            const sessionMetrics = sessionManager.getSessionMetrics(sessionId);
            const recoveryStatus = contextManager.getRecoveryStatus(sessionId);
            
            metrics.connectionDetails.push({
                sessionId,
                sessionType: connectionData.sessionType || 'unknown',
                isActive: connectionData.isSessionActive,
                startTime: sessionMetrics?.startTime,
                lastActivity: sessionMetrics?.lastActivity,
                messagesReceived: sessionMetrics?.messagesReceived || 0,
                messagesSent: sessionMetrics?.messagesSent || 0,
                recoveryCount: sessionMetrics?.recoveryCount || 0,
                recoveryStatus
            });
        }

        return {
            success: true,
            metrics
        };
    });

    // Provider health check
    fastify.get('/api/provider-health', async (_request: FastifyRequest, _reply: FastifyReply) => {
        // This would check Gemini API health, Twilio connectivity, etc.
        return {
            success: true,
            providers: {
                gemini: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                },
                twilio: {
                    status: 'healthy',
                    lastCheck: new Date().toISOString()
                }
            }
        };
    });

    // Manual recovery trigger
    fastify.post<{ Params: TriggerRecoveryParams }>('/api/trigger-recovery/:callSid', async (request, _reply) => {
        try {
            const { callSid } = request.params;
            const connectionData = activeConnections.get(callSid);
            
            if (!connectionData) {
                return { success: false, message: 'Session not found' };
            }

            // Mark as interrupted and trigger recovery
            contextManager.markSessionInterrupted(callSid, 'manual_recovery');
            
            // Trigger recovery
            setTimeout(() => {
                sessionManager.recoverSession(callSid, 'manual_recovery');
            }, 1000);

            return {
                success: true,
                message: 'Recovery triggered',
                callSid
            };
        } catch (error) {
            return {
                success: false,
                error: (error as Error).message
            };
        }
    });

    // Audio settings endpoints
    fastify.post<{ Body: AudioSettingsBody }>('/api/audio-settings', async (request, reply) => {
        try {
            const validatedSettings = SecurityUtils.validateAudioSettings(request.body);

            if (!validatedSettings) {
                reply.status(400);
                return { success: false, error: 'Invalid audio settings' };
            }

            // Update settings in AudioProcessor
            const audioProcessor = new AudioProcessor();
            audioProcessor.updateAudioSettings(validatedSettings);

            return {
                success: true,
                settings: audioProcessor.getAudioSettings(),
                message: 'Audio settings updated successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error updating audio settings:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get('/api/audio-settings', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const audioProcessor = new AudioProcessor();
            return {
                success: true,
                settings: audioProcessor.getAudioSettings(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio settings:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Audio quality metrics endpoint
    fastify.get('/api/audio-quality', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            let qualityMetrics;
            try {
                qualityMetrics = (AudioProcessor as any).audioQualityMonitor?.getSummary();
            } catch (e) {
                qualityMetrics = null;
            }
            
            const audioQuality = qualityMetrics || {
                sampleRate: 16000,
                bitDepth: 16,
                channels: 1,
                format: 'PCM',
                averageLatency: 50,
                packetsProcessed: 0,
                errors: 0
            };
            
            return {
                success: true,
                audioQuality: audioQuality,
                debugMode: process.env.AUDIO_DEBUG === 'true',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Reset audio quality metrics endpoint
    fastify.post('/api/audio-quality/reset', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            // Check if audioQualityMonitor exists before calling reset
            const audioQualityMonitor = (AudioProcessor as any).audioQualityMonitor;
            if (audioQualityMonitor && typeof audioQualityMonitor.reset === 'function') {
                audioQualityMonitor.reset();
            }
            return {
                success: true,
                message: 'Audio quality metrics reset successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error resetting audio quality metrics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Campaign script endpoints (UI compatibility)

    // Get campaign script by ID (for UI compatibility)
    fastify.get<{ Params: CampaignScriptParams }>('/get-campaign-script/:id', async (request, reply) => {
        try {
            const { id } = request.params;

            if (!SecurityUtils.validateScriptId(id)) {
                reply.status(400);
                return { error: 'Invalid script ID' };
            }

            // Handle unified ID system: 1-6 = outbound, 7-12 = incoming
            const numericId = parseInt(id);
            
            // Check if parseInt returned a valid number
            if (isNaN(numericId)) {
                return {
                    success: false,
                    error: 'Invalid campaign ID format. Must be a number.'
                };
            }
            
            let campaignScript = null;

            if (numericId >= 7 && numericId <= 12) {
                // IDs 7-12 are incoming campaigns (map to incoming-campaign1.json to incoming-campaign6.json)
                const incomingCampaignId = numericId - 6; // 7->1, 8->2, ..., 12->6
                campaignScript = loadCampaignScript(incomingCampaignId, 'incoming', false);
                console.log(`✅ Loading incoming campaign ${incomingCampaignId} for ID ${id}`);
            } else if (numericId >= 1 && numericId <= 6) {
                // IDs 1-6 are outbound campaigns (map to campaign1.json to campaign6.json)
                campaignScript = loadCampaignScript(numericId, 'outbound', false);
                console.log(`✅ Loading outbound campaign ${numericId} for ID ${id}`);
            }

            if (campaignScript) {
                // Return the campaign script directly in the format expected by the frontend
                return campaignScript;
            } else {
                reply.status(404);
                return { error: `Campaign script ${id} not found` };
            }
        } catch (error) {
            console.error('❌ Error getting campaign script:', error);
            reply.status(500);
            return { error: (error as Error).message };
        }
    });

    // Get incoming campaign script (alternative route for UI compatibility)
    fastify.get<{ Params: IncomingCampaignParams }>('/incoming-campaign/:id', async (request, reply) => {
        try {
            const id = request.params.id.replace('.json', ''); // Remove .json if present
            console.log('📥 [DEBUG] Incoming campaign script request for ID:', id);

            if (!id) {
                console.log('❌ [DEBUG] No ID provided');
                reply.status(400);
                return { success: false, error: 'Script ID is required' };
            }

            if (!SecurityUtils.validateScriptId(id)) {
                console.log('❌ [DEBUG] Invalid script ID:', id);
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const script = scriptManager.getScriptConfig(id, true);
            console.log('📊 [DEBUG] Script found:', !!script);

            if (script) {
                // Return the script as-is without any conversion
                return script;
            } else {
                console.log('❌ [DEBUG] Script not found for ID:', id);
                reply.status(404);
                return { success: false, error: 'Incoming script not found' };
            }
        } catch (error) {
            console.error('❌ Error getting incoming campaign script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get incoming campaign script (for UI compatibility)
    fastify.get<{ Params: IncomingCampaignParams }>('/incoming-campaign\\::id.json', async (request, reply) => {
        try {
            const id = request.params.id;
            console.log('📥 [DEBUG] Incoming campaign script request for ID:', id);

            if (!id) {
                console.log('❌ [DEBUG] No ID provided');
                reply.status(400);
                return { success: false, error: 'Script ID is required' };
            }

            if (!SecurityUtils.validateScriptId(id)) {
                console.log('❌ [DEBUG] Invalid script ID:', id);
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const script = scriptManager.getScriptConfig(id, true);
            console.log('📊 [DEBUG] Script found:', !!script);

            if (script) {
                // Convert to the format expected by the frontend - no hardcoded instructions
                const campaignData = {
                    campaign: script.scriptId || id,
                    agentPersona: {
                        name: 'AI Assistant',
                        tone: '' // Campaign script should define tone
                    },
                    script: {
                        instructions: script.aiInstructions || '' // Campaign script should provide all instructions
                    },
                    customerData: {
                        handling: '' // Campaign script should define handling
                    },
                    transferData: {
                        protocols: '' // Campaign script should define protocols
                    }
                };

                return campaignData;
            } else {
                console.log('❌ [DEBUG] Script not found for ID:', id);
                reply.status(404);
                return { success: false, error: 'Incoming script not found' };
            }
        } catch (error) {
            console.error('❌ Error getting incoming campaign script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Script management endpoints
    fastify.get('/api/scripts/incoming', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getIncomingScripts();
            return {
                success: true,
                scripts,
                current: scriptManager.getCurrentIncomingScript(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting incoming scripts:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get('/api/scripts/outbound', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            return {
                success: true,
                scripts,
                current: scriptManager.getCurrentOutboundScript(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting outbound scripts:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.post<{ Params: ScriptIdParams }>('/api/scripts/incoming/:scriptId/activate', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setIncomingScript(scriptId);

            if (success) {
                return {
                    success: true,
                    message: `Incoming script '${scriptId}' activated`,
                    scriptId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return { success: false, error: 'Script not found' };
            }
        } catch (error) {
            console.error('❌ Error activating incoming script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.post<{ Params: ScriptIdParams }>('/api/scripts/outbound/:scriptId/activate', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setOutboundScript(scriptId);

            if (success) {
                return {
                    success: true,
                    message: `Outbound script '${scriptId}' activated`,
                    scriptId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return { success: false, error: 'Script not found' };
            }
        } catch (error) {
            console.error('❌ Error activating outbound script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Session lifecycle management endpoints
    fastify.get('/api/sessions/active', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const activeSessions = lifecycleManager ? lifecycleManager.getActiveSessions() : [];
            return {
                success: true,
                sessions: activeSessions,
                count: activeSessions.length,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting active sessions:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get<{ Params: SessionIdParams }>('/api/sessions/:sessionId/status', async (request, reply) => {
        try {
            const { sessionId } = request.params;

            if (!SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }

            const sessionStatus = lifecycleManager ? lifecycleManager.getSessionStatus(sessionId) : { exists: false };
            const recoveryStatus = recoveryManager ? recoveryManager.getRecoveryStatus(sessionId) : null;
            const summaryStatus = summaryManager ? summaryManager.getSummaryStatus(sessionId) : null;

            return {
                success: true,
                sessionId,
                session: sessionStatus,
                recovery: recoveryStatus,
                summary: summaryStatus,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting session status:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.post<{ Params: SessionIdParams; Body: EndSessionBody }>(
        '/api/sessions/:sessionId/end',
        async (request, reply) => {
        try {
            const { sessionId } = request.params;
            const { reason = 'api_request' } = request.body;

            if (!SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }

            const connectionData = activeConnections.get(sessionId);
            if (!connectionData) {
                reply.status(404);
                return { success: false, error: 'Session not found' };
            }

            if (lifecycleManager) {
                const success = await lifecycleManager.requestSessionEnd(sessionId, connectionData, reason);

                if (success) {
                    activeConnections.delete(sessionId);
                    return {
                        success: true,
                        message: 'Session end requested successfully',
                        sessionId,
                        reason,
                        timestamp: new Date().toISOString()
                    };
                } else {
                    reply.status(500);
                    return { success: false, error: 'Failed to end session' };
                }
            } else {
                reply.status(500);
                return { success: false, error: 'Lifecycle manager not available' };
            }
        } catch (error) {
            console.error('❌ Error ending session:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.post<{ Params: SessionIdParams }>('/api/sessions/:sessionId/summary', async (request, reply) => {
        try {
            const { sessionId } = request.params;

            if (!SecurityUtils.sanitizeCallSid(sessionId) && !sessionId.startsWith('local-')) {
                reply.status(400);
                return { success: false, error: 'Invalid session ID format' };
            }

            const connectionData = activeConnections.get(sessionId);
            if (!connectionData) {
                reply.status(404);
                return { success: false, error: 'Session not found' };
            }

            if (summaryManager) {
                const success = await summaryManager.requestSummary(sessionId, connectionData, contextManager);

                return {
                    success,
                    message: success ? 'Summary requested successfully' : 'Failed to request summary',
                    sessionId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(500);
                return { success: false, error: 'Summary manager not available' };
            }
        } catch (error) {
            console.error('❌ Error requesting summary:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });


    // Call Management Endpoints (UI compatibility)

    // Update session configuration
    fastify.post<{ Body: UpdateSessionConfigBody }>('/update-session-config', async (request, reply) => {
        try {
            const { voice, model, aiInstructions, targetName, targetPhoneNumber } = request.body;

            // Get current config from webhook module
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL,
                targetName: null,
                targetPhoneNumber: null,
                phoneNumber: '',
                mode: 'outbound'
            };

            // Update next call configuration
            if (voice) {
                const validation = voiceManager.validateVoice(voice);
                if (validation.isValid) {
                    currentConfig.voice = validation.voice!;
                }
            }

            if (model) {
                const validation = modelManager.validateModel(model);
                if (validation.isValid) {
                    currentConfig.model = validation.model!;
                }
            }

            if (aiInstructions) {
                currentConfig.aiInstructions = aiInstructions;
            }

            if (targetName) {
                currentConfig.targetName = targetName;
            }

            if (targetPhoneNumber) {
                currentConfig.targetPhoneNumber = targetPhoneNumber;
            }

            // Update config in webhook module
            (fastify as any).setNextCallConfig(currentConfig);

            return {
                status: 'success',
                success: true,
                config: currentConfig,
                message: 'Session configuration updated',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error updating session config:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Make outbound call - Real Twilio integration
    fastify.post<{ Body: MakeCallBody }>('/make-call', {
        schema: {
            body: {
                type: 'object',
                required: ['to', 'from'],
                properties: {
                    to: { type: 'string' },
                    from: { type: 'string' },
                    task: { type: 'string' },
                    voice: { type: 'string' },
                    model: { type: 'string' },
                    targetName: { type: 'string' },
                    targetPhoneNumber: { type: 'string' },
                    outputLanguage: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { to, from, task, voice, model, targetName, targetPhoneNumber, outputLanguage } = request.body;

            // Input validation and sanitization
            const sanitizedTo = SecurityUtils.validatePhoneNumber(to);
            const sanitizedFrom = SecurityUtils.validatePhoneNumber(from);

            if (!sanitizedTo) {
                reply.status(400);
                return { error: 'Invalid "to" phone number format. Use international format (+**********).' };
            }

            if (!sanitizedFrom) {
                reply.status(400);
                return { error: 'Invalid "from" phone number format. Use international format (+**********).' };
            }

            const client = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

            // Get the current outbound script configuration
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL,
                targetName: null,
                targetPhoneNumber: null,
                phoneNumber: '',
                mode: 'outbound'
            };
            let callConfig = { ...currentConfig };
            
            // Try to get the active outbound script
            try {
                const currentScript = scriptManager.getCurrentOutboundScript();
                if (currentScript) {
                    const scriptConfig = scriptManager.getScriptConfig(currentScript.id, false);
                    if (scriptConfig) {
                        callConfig = {
                            ...scriptConfig,
                            // Allow overrides from request
                            voice: voice || scriptConfig.voice,
                            model: model || scriptConfig.model,
                            targetName: targetName || scriptConfig.targetName,
                            targetPhoneNumber: targetPhoneNumber || scriptConfig.targetPhoneNumber
                        } as CallConfig;
                        console.log(`📋 Using outbound script ${currentScript.id} for call`);
                    }
                }
            } catch (error) {
                console.warn('⚠️ Error getting outbound script:', error);
            }
            
            // If task is provided, use it as a simple override (not recommended - use campaign scripts)
            if (task) {
                console.log('⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)');
                callConfig.aiInstructions = task;
            }
            
            // Update callConfig in webhook module with the final configuration
            (fastify as any).setNextCallConfig(callConfig);

            const call = await client.calls.create({
                to: sanitizedTo,
                from: sanitizedFrom,
                url: `${PUBLIC_URL}/incoming-call`,
                statusCallback: `${PUBLIC_URL}/call-status`,
                statusCallbackEvent: [
                    'initiated', 'ringing', 'answered', 'completed',
                    'failed', 'canceled', 'no-answer', 'busy'
                ],
                statusCallbackMethod: 'POST',
                record: true,
                recordingStatusCallback: `${PUBLIC_URL}/recording-status`,
                recordingStatusCallbackMethod: 'POST',
                recordingStatusCallbackEvent: ['completed']
            });

            console.log(`📞 [${call.sid}] Outbound call initiated to ${to}`);
            return { callSid: call.sid };

        } catch (error) {
            console.error('❌ Error making call:', error);
            reply.status(500);
            return { error: 'Failed to initiate call', details: (error as Error).message };
        }
    });

    // Get call results
    fastify.get<{ Params: CallResultsParams }>('/call-results/:callSid', async (request, reply) => {
        try {
            const { callSid } = request.params;

            if (!SecurityUtils.sanitizeCallSid(callSid)) {
                reply.status(404);
                return { success: false, error: 'Invalid call SID format' };
            }

            // Check if session exists in context manager
            const sessionContext = contextManager.getSessionContext(callSid);
            const sessionStatus = lifecycleManager ? lifecycleManager.getSessionStatus(callSid) : null;

            if (sessionContext || sessionStatus) {
                return {
                    success: true,
                    callSid,
                    status: sessionStatus?.status || 'unknown',
                    context: sessionContext,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return {
                    success: false,
                    error: 'Call results not found',
                    callSid
                };
            }
        } catch (error) {
            console.error('❌ Error getting call results:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // === ADDITIONAL SCRIPT MANAGEMENT ENDPOINTS ===

    // Get all available outbound call scripts
    fastify.get('/api/outbound-scripts', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            const currentScript = scriptManager.getCurrentOutboundScript();
            reply.send({
                success: true,
                scripts: scripts,
                currentScript: {
                    id: currentScript?.id || 'default',
                    name: currentScript?.name || 'Default Script',
                    description: currentScript?.description || 'Default outbound script'
                }
            });
        } catch (error) {
            console.error('Error listing outbound scripts:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Backward compatibility endpoint (returns outbound scripts)
    fastify.get('/api/incoming-scripts', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            const currentScript = scriptManager.getCurrentOutboundScript();
            reply.send({
                success: true,
                scripts: scripts,
                currentScript: {
                    id: currentScript?.id || 'default',
                    name: currentScript?.name || 'Default Script',
                    description: currentScript?.description || 'Default outbound script'
                }
            });
        } catch (error) {
            console.error('Error listing incoming scripts:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get current active incoming call script
    fastify.get('/api/incoming-scripts/current', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const currentScript = scriptManager.getCurrentIncomingScript();
            reply.send({
                success: true,
                currentScript: currentScript || {
                    id: 'customer-service',
                    name: 'Customer Service',
                    description: 'Default customer service script'
                }
            });
        } catch (error) {
            console.error('Error getting current incoming script:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Set active incoming call script
    fastify.post<{ Params: ScriptIdParams }>('/api/incoming-scripts/set/:scriptId', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setIncomingScript(scriptId);

            if (success) {
                return reply.send({
                    success: true,
                    message: `Incoming script set to: ${scriptId}`,
                    scriptId,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(404);
                return reply.send({
                    success: false,
                    error: 'Script not found'
                });
            }
        } catch (error) {
            console.error('Error setting incoming script:', error);
            return reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    console.log('🔍 [DEBUG] About to register incoming scenarios route...');
    // Get all available incoming scenarios (NEW SYSTEM)
    console.log('🔧 Registering /api/incoming-scenarios route...');
    console.log('🔍 [DEBUG] scriptManager available:', !!scriptManager);
    console.log('🔍 [DEBUG] scriptManager.getIncomingScripts available:', typeof scriptManager?.getIncomingScripts);

    fastify.get('/api/incoming-scenarios', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            console.log('📥 [DEBUG] /api/incoming-scenarios called');
            console.log('🔍 [DEBUG] scriptManager in route:', !!scriptManager);
            const scenarios = scriptManager.getIncomingScripts();
            console.log('📊 [DEBUG] Found scenarios:', scenarios?.length || 0);
            reply.send({
                success: true,
                scenarios: scenarios.map((script: any) => ({
                    id: script.id,
                    name: script.name,
                    description: script.description,
                    category: script.category || 'support'
                })),
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting incoming scenarios:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });
    console.log('✅ /api/incoming-scenarios route registered successfully');

    // Select active incoming scenario (NEW SYSTEM)
    console.log('🔧 Registering /api/incoming-scenarios/select route...');
    fastify.post<{ Body: SelectScenarioBody }>('/api/incoming-scenarios/select', async (request, reply) => {
        try {
            const { scenarioId } = request.body;

            if (!scenarioId) {
                reply.status(400);
                return { success: false, error: 'Scenario ID is required' };
            }

            if (!SecurityUtils.validateScriptId(scenarioId)) {
                reply.status(400);
                return { success: false, error: 'Invalid scenario ID' };
            }

            const success = scriptManager.setIncomingScript(scenarioId);

            if (success) {
                return reply.send({
                    success: true,
                    message: `Incoming scenario selected: ${scenarioId}`,
                    scenarioId,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(404);
                return reply.send({
                    success: false,
                    error: 'Scenario not found'
                });
            }
        } catch (error) {
            console.error('Error selecting incoming scenario:', error);
            return reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });
    console.log('✅ /api/incoming-scenarios/select route registered successfully');

    // Configure incoming scenario (UI compatibility)
    console.log('🔧 Registering /api/configure-incoming-scenario route...');
    try {
        fastify.post<{ Body: ConfigureIncomingScenarioBody }>(
            '/api/configure-incoming-scenario',
            async (request, reply) => {
            try {
                console.log('📥 [DEBUG] configure-incoming-scenario called with body:', request.body);
                const { scenarioId, name, language, fromNumber, voice, model, country, script, isActive } = request.body;

                // Support both new scenarioId format and legacy format
                const targetScenarioId = scenarioId || name;

                if (!targetScenarioId) {
                    console.log('❌ [DEBUG] Missing scenarioId/name in request');
                    reply.status(400);
                    return { success: false, error: 'Scenario ID or name is required' };
                }

                console.log('🔍 [DEBUG] Using scenarioId:', targetScenarioId);

                if (!SecurityUtils.validateScriptId(targetScenarioId)) {
                    console.log('❌ [DEBUG] Invalid scenarioId:', targetScenarioId);
                    reply.status(400);
                    return { success: false, error: 'Invalid scenario ID' };
                }

                console.log('🔧 [DEBUG] Calling scriptManager.setIncomingScript...');
                const success = scriptManager.setIncomingScript(targetScenarioId);
                console.log('📊 [DEBUG] setIncomingScript result:', success);

                if (success) {
                    const result = {
                        success: true,
                        scenarioId: targetScenarioId,
                        message: `Incoming scenario '${targetScenarioId}' configured`,
                        timestamp: new Date().toISOString()
                    };
                    console.log('✅ [DEBUG] Returning success result:', result);
                    return result;
                } else {
                    console.log('❌ [DEBUG] setIncomingScript failed');
                    reply.status(404);
                    return { success: false, error: 'Scenario not found' };
                }
            } catch (error) {
                console.error('❌ Error configuring incoming scenario:', error);
                reply.status(500);
                return { success: false, error: (error as Error).message };
            }
        });
        console.log('✅ /api/configure-incoming-scenario route registered successfully');
    } catch (error) {
        console.error('❌ Error registering /api/configure-incoming-scenario route:', error);
    }

    // Analytics and System Metrics Endpoints

    // Get system analytics
    fastify.get('/api/analytics/system', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const analytics = {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                activeConnections: activeConnections.size,
                sessionStats: {
                    total: contextManager.getContextStats().totalSessions || 0,
                    active: lifecycleManager ? lifecycleManager.getActiveSessions().length : 0,
                    recovered: recoveryManager ? recoveryManager.getRecoveryStats().totalRecovered : 0
                },
                audioStats: {
                    // Get audio processing stats from AudioProcessor
                    qualityMonitor: (AudioProcessor as any).audioQualityMonitor
                        ? (AudioProcessor as any).audioQualityMonitor.getSummary()
                        : null
                },
                voiceModelStats: {
                    availableVoices: Object.keys(voiceManager.getAvailableVoices()).length,
                    currentVoice: voiceManager.getDefaultVoice(),
                    availableModels: Object.keys(modelManager.getAvailableModels()).length,
                    currentModel: modelManager.getCurrentModel()
                },
                timestamp: new Date().toISOString()
            };

            return {
                success: true,
                analytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting system analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get audio processing metrics
    fastify.get('/api/analytics/audio', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const audioAnalytics = {
                qualityMonitor: (AudioProcessor as any).audioQualityMonitor
                    ? (AudioProcessor as any).audioQualityMonitor.getSummary()
                    : null,
                enhancerStats: (AudioProcessor as any).audioEnhancer
                    ? (AudioProcessor as any).audioEnhancer.getProcessingStats()
                    : null,
                transcriptionHealth: null as any
            };

            // Get transcription health if available
            try {
                const transcriptionManager = new TranscriptionManager();
                audioAnalytics.transcriptionHealth = await transcriptionManager.healthCheck();
            } catch (error) {
                console.warn('⚠️ Could not get transcription health:', (error as Error).message);
            }

            return {
                success: true,
                audioAnalytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting audio analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get session analytics
    fastify.get('/api/analytics/sessions', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const sessionAnalytics = {
                contextStats: contextManager.getContextStats(),
                lifecycleStats: lifecycleManager ? lifecycleManager.getSessionStats() : null,
                recoveryStats: recoveryManager ? recoveryManager.getRecoveryStats() : null,
                healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                activeConnections: Array.from(activeConnections.keys())
            };

            return {
                success: true,
                sessionAnalytics,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting session analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Reset audio processing statistics
    fastify.post('/api/analytics/audio/reset', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            // Reset audio quality monitor
            if ((AudioProcessor as any).audioQualityMonitor && (AudioProcessor as any).audioQualityMonitor.reset) {
                (AudioProcessor as any).audioQualityMonitor.reset();
            }

            // Reset audio enhancer stats
            if ((AudioProcessor as any).audioEnhancer && (AudioProcessor as any).audioEnhancer.resetProcessingStats) {
                (AudioProcessor as any).audioEnhancer.resetProcessingStats();
            }

            return {
                success: true,
                message: 'Audio processing statistics reset',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error resetting audio statistics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get comprehensive system metrics (for monitoring/alerting)
    fastify.get('/api/metrics', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const metrics = {
                system: {
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    cpu: process.cpuUsage(),
                    version: process.version,
                    platform: process.platform
                },
                application: {
                    activeConnections: activeConnections.size,
                    healthScore: healthMonitor ? healthMonitor.getHealthScore() : 100,
                    contextStats: contextManager.getContextStats(),
                    nextCallConfig: await (async () => {
                        const config = await (fastify as any).getNextCallConfig();
                        return config ? {
                            voice: config.voice,
                            model: config.model,
                            hasInstructions: !!config.aiInstructions,
                            hasTarget: !!config.targetPhoneNumber
                        } : null;
                    })()
                },
                services: {
                    sessionManager: sessionManager ? 'available' : 'unavailable',
                    contextManager: contextManager ? 'available' : 'unavailable',
                    healthMonitor: healthMonitor ? 'available' : 'unavailable',
                    recoveryManager: recoveryManager ? 'available' : 'unavailable',
                    lifecycleManager: lifecycleManager ? 'available' : 'unavailable'
                },
                timestamp: new Date().toISOString()
            };

            return metrics;
        } catch (error) {
            console.error('❌ Error getting system metrics:', error);
            reply.status(500);
            return { error: 'Failed to get system metrics', message: (error as Error).message };
        }
    });

    // Test route to verify route registration is working
    console.log('🔧 Registering test route...');
    try {
        fastify.get('/api/test', async (request: FastifyRequest, reply: FastifyReply) => {
            console.log('📥 [DEBUG] Test route called');
            return { success: true, message: 'Test route working', timestamp: new Date().toISOString() };
        });
        console.log('✅ Test route registered successfully');
    } catch (error) {
        console.error('❌ Error registering test route:', error);
    }

    console.log('🔍 [DEBUG] Reached end of route registration function');
    // Missing routes for test compatibility
    
    // Incoming scripts endpoints
    fastify.get('/incoming-scripts', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts(); // Returns outbound scripts for compatibility
            return scripts.map((script: any) => ({
                id: script.id,
                name: script.name,
                description: script.description,
                systemPrompt: script.aiInstructions,
                campaignScript: script.script
            }));
        } catch (error) {
            console.error('❌ Error getting incoming scripts:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get('/incoming-scripts/current', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const current = scriptManager.getCurrentOutboundScript(); // Returns outbound script for compatibility
            return {
                id: current?.id || 'default',
                name: current?.name || 'Default Script',
                systemPrompt: current?.aiInstructions || '',
                campaignScript: current?.script || ''
            };
        } catch (error) {
            console.error('❌ Error getting current incoming script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Analytics endpoint
    fastify.get('/analytics', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            return {
                success: true,
                totalOutboundCalls: 0,
                totalInboundCalls: 0,
                callHistory: [] as any[],
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting analytics:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    console.log('✅ All API routes registered successfully!');
}

// Helper function to end sessions
function endSessionHelper(
    sessionId: string,
    deps: {
        sessionManager: any;
        contextManager: any;
        activeConnections: Map<string, any>
    }
): void {
    const { sessionManager, contextManager, activeConnections } = deps;

    try {
        console.log(`🔚 [${sessionId}] Ending session via API`);

        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Close Gemini session
            if (connectionData.geminiSession) {
                try {
                    connectionData.geminiSession.close();
                } catch (error) {
                    console.warn(`⚠️ [${sessionId}] Error closing Gemini session:`, error);
                }
            }

            // Log summary if available
            if (connectionData.summaryText) {
                console.log(`📋 [${sessionId}] Final Summary: ${connectionData.summaryText}`);
            }

            // Remove from active connections
            activeConnections.delete(sessionId);
        }

        // Clean up context and session manager
        contextManager.clearSessionContext(sessionId);
        sessionManager.cleanupSession(sessionId);

        console.log(`✅ [${sessionId}] Session ended via API`);

    } catch (error) {
        console.error(`❌ [${sessionId}] Error ending session via API:`, error);
    }
}