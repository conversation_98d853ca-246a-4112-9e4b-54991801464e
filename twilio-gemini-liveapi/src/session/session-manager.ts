import { Modality } from '../gemini/client';
import { AudioProcessor } from '../audio/audio-processor';
import { forwardAudio, initializeAudioForwarding, cleanupAudioForwarding } from '../audio/audio-forwarding';
import { standardizeConnectionData } from '../utils/websocket-utils';
import { timerManager } from '../utils/timer-manager';
import {
    ConnectionData,
    GeminiSession,
    GeminiClient,
    GeminiLiveMessage,
    GeminiRealtimeInput,
    GeminiError,
    ConversationEntry,
    TranscriptEntry,
    SpeechTranscriptEntry
} from '../types/global';
import { ContextManager } from './context-manager';

interface SessionConfig {
    model: string;
    voice: string;
    aiInstructions?: string;
    sessionType?: string;
    isIncomingCall?: boolean;
    scriptId?: string | null;
    campaignId?: string | null;
    scriptType?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}

interface SessionMetrics {
    startTime: number;
    messagesReceived: number;
    messagesSent: number;
    recoveryCount: number;
    lastActivity: number;
    isInitializing: boolean;
    lastRecoveryTime?: number;
}

interface ConversationEntry {
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
    messageId?: string;
}

interface TranscriptEntry {
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
    confidence?: number;
}

interface SpeechTranscriptEntry {
    text: string;
    timestamp: number;
    confidence: number;
}



interface ExtendedConnectionData extends ConnectionData {
    sessionConfig?: SessionConfig;
    sessionReady?: boolean;
    sessionInitialized?: number;
    geminiSessionError?: string;
    // Other properties are inherited from ConnectionData
    isTwilioCall?: boolean;
    streamSid?: string;
}

// Bounded Map and Set for memory safety
class BoundedMap<K, V> extends Map<K, V> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    set(key: K, value: V): this {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            if (firstKey !== undefined) {
                this.delete(firstKey);
                console.log(`🧹 SessionManager BoundedMap: Removed oldest entry ${firstKey}`);
            }
        }
        return super.set(key, value);
    }
}

class BoundedSet<T> extends Set<T> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    add(value: T): this {
        if (this.size >= this.maxSize && !this.has(value)) {
            const firstValue = this.values().next().value;
            if (firstValue !== undefined) {
                this.delete(firstValue);
                console.log(`🧹 SessionManager BoundedSet: Removed oldest entry ${firstValue}`);
            }
        }
        return super.add(value);
    }
}

// Session Manager for Gemini connections with recovery
export class SessionManager {
    private contextManager: ContextManager;
    private geminiClient: GeminiClient;
    private recoveryInProgress: BoundedSet<string>;
    private audioProcessor: AudioProcessor;
    private sessionMetrics: BoundedMap<string, SessionMetrics>;
    private activeConnections: Map<string, ConnectionData> | null;

    constructor(
        contextManager: ContextManager,
        geminiClient: GeminiClient,
        activeConnections: Map<string, ConnectionData> | null = null
    ) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new BoundedSet<string>(200); // Limit recovery operations
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new BoundedMap<string, SessionMetrics>(1000); // Limit session metrics
        this.activeConnections = activeConnections; // Reference to activeConnections for audio forwarding
    }

    // Utility function to maintain bounded arrays and prevent memory leaks
    private addToBoundedArray<T>(array: T[], item: T, maxSize: number): T[] {
        if (!Array.isArray(array)) {
            console.warn('addToBoundedArray: array is not an array, initializing as empty array');
            array = [];
        }

        array.push(item);

        // Remove oldest entries if limit exceeded
        if (array.length > maxSize) {
            const removed = array.splice(0, array.length - maxSize);
            console.log(`🧹 Trimmed ${removed.length} old entries from bounded array (max: ${maxSize})`);
        }

        return array;
    }

    // Create new Gemini session
    async createGeminiSession(
        callSid: string,
        config: SessionConfig,
        connectionData: ConnectionData
    ): Promise<GeminiSession | null> {
        const sessionStartTime = Date.now();
        const extConnectionData = connectionData as ExtendedConnectionData;
        
        try {
            console.log(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            console.log(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            console.log(`🔍 [${callSid}] config.model = "${config.model}"`);
            console.log(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            console.log(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            console.log(`🔍 [${callSid}] ==========================================`);

            // Use arrow functions to maintain 'this' context instead of aliasing

            // Initialize session metrics BEFORE creating session to avoid race conditions
            this.sessionMetrics.set(callSid, {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now(),
                isInitializing: true
            });

            // Store reference immediately to prevent race conditions
            extConnectionData.geminiSession = null;
            extConnectionData.isSessionActive = false;

            // Store config for callback access
            extConnectionData.sessionConfig = config;

            // Create session and store reference immediately
            // Add call direction context to system instructions
            let systemInstruction = config.aiInstructions;
            console.log(`🎯 [${callSid}] ===== SYSTEM INSTRUCTION DEBUG =====`);
            console.log(`🎯 [${callSid}] Has AI instructions: ${!!config.aiInstructions}`);
            console.log(`🎯 [${callSid}] Instruction length: ${systemInstruction?.length || 0}`);
            console.log(`🎯 [${callSid}] Script ID: ${config.scriptId}`);
            console.log(`🎯 [${callSid}] Campaign ID: ${config.campaignId}`);
            console.log(`🎯 [${callSid}] Script type: ${config.scriptType}`);
            const instructionPreview = systemInstruction?.substring(0, 200) || 'NO INSTRUCTIONS';
            console.log(`🎯 [${callSid}] First 200 chars: ${instructionPreview}...`);
            console.log(`🎯 [${callSid}] ====================================`);

            // Add context based on call direction for Twilio calls
            if (config.sessionType === 'twilio_call' && systemInstruction) {
                if (!config.isIncomingCall) {
                    // Outbound calls: AI initiates the conversation
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are making an outbound call. When the call connects, introduce yourself and ' +
                        'begin the conversation according to the script above. Start speaking immediately when the call is answered.';
                } else {
                    // Inbound calls: AI should greet the caller and wait for their response
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are receiving an inbound call. When the customer speaks, greet them warmly and ' +
                        'assist them according to the script above.';
                }
            }

            // Fallback for inbound calls without instructions
            if (!systemInstruction && config.isIncomingCall && config.sessionType === 'twilio_call') {
                console.warn(`No AI instructions found for inbound call, using fallback`);
                systemInstruction = 'You are a helpful customer service representative. ' +
                    'Greet the caller warmly and ask how you can help them today.';
            }

            // CRITICAL DEBUG: Log full configuration being sent to Gemini
            console.log(`🔍 [${callSid}] Attempting Gemini connection with config:`, {
                model: config.model,
                voice: config.voice,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall,
                hasSystemInstruction: !!systemInstruction,
                instructionLength: systemInstruction?.length,
                hasGeminiClient: !!this.geminiClient,
                hasLiveAPI: !!this.geminiClient?.live
            });

            if (systemInstruction) {
                console.log(`🎯 [${callSid}] System instruction preview: ${systemInstruction.substring(0, 300)}...`);
            } else {
                console.error(`❌ [${callSid}] CRITICAL: No system instruction available!`);
            }

            console.log(`🚀 [${callSid}] Connecting to Gemini Live API with system instruction...`);
            const geminiSession = await this.geminiClient.live.connect({
                model: config.model,
                systemInstruction: systemInstruction ? {
                    parts: [{
                        text: systemInstruction
                    }]
                } : undefined,
                generationConfig: {
                    responseModalities: ['AUDIO'],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Gemini session opened`);
                        extConnectionData.isSessionActive = true;

                        // Update metrics state
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.isInitializing = false;
                        }

                        // Save initial context with bounded arrays
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...extConnectionData,
                            conversationLog: [],
                            fullTranscript: [],
                            speechTranscript: [], // Initialize bounded speech transcript
                            maxConversationLogSize: 500,
                            maxTranscriptSize: 1000,
                            maxSpeechTranscriptSize: 1000 // Add bounds for speech transcript
                        });

                        // CRITICAL FIX: Live API setup is now handled in initial connection configuration
                        console.log(`✅ [${callSid}] Live API setup configuration included in connection`);
                        if (config.aiInstructions) {
                            const instructionSnippet = config.aiInstructions.substring(0, 200);
                            console.log(`🎯 [${callSid}] AI instructions included: ${instructionSnippet}...`);
                        } else {
                            console.warn(`⚠️ [${callSid}] No AI instructions in config - session may not work properly`);
                        }

                        // Validate session setup
                        console.log(`🔍 [${callSid}] Session validation:`, {
                            hasSystemInstruction: !!systemInstruction,
                            instructionLength: systemInstruction?.length,
                            model: config.model,
                            voice: config.voice,
                            sessionActive: extConnectionData.isSessionActive
                        });

                        console.log(`🔄 [${callSid}] Session initialized and ready for conversation`);
                    },

                    onerror: (error: GeminiError) => {
                        console.error(`❌ [${callSid}] Gemini session error:`, error);
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        extConnectionData.isSessionActive = false;
                        extConnectionData.geminiSessionError = error.message;

                        console.log(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: (event: CloseEvent) => {
                        const sessionDuration = Date.now() - sessionStartTime;
                        console.log(`🔌 [${callSid}] Gemini session closed after ${sessionDuration}ms`);
                        console.log(`🔌 [${callSid}] Close event details:`, {
                            code: event?.code,
                            reason: event?.reason,
                            wasClean: event?.wasClean,
                            sessionDuration: sessionDuration
                        });

                        extConnectionData.isSessionActive = false;

                        // Check for early session termination (likely initialization failure)
                        if (sessionDuration < 5000) {
                            console.error(
                                `❌ [${callSid}] Session closed too quickly (${sessionDuration}ms) - likely initialization failure`
                            );
                            console.error(`❌ [${callSid}] This suggests API key, model, or configuration issues`);
                        }

                        // Session closed - cleanup handled by lifecycle manager

                        // Check if this is an unexpected close (connection still active)
                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = extConnectionData.ws && extConnectionData.ws.readyState === 1;

                        if (isUnexpectedClose) {
                            console.log(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            console.log(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            console.log(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message: GeminiLiveMessage) => {
                        try {
                            // Log Gemini API messages for debugging (excluding audio packets)
                            const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                            if (!hasAudio) {
                                console.log(`📨 [${callSid}] Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                            } else {
                                const audioDataLength = message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0;
                                console.log(`🎵 [${callSid}] Gemini audio packet received (${audioDataLength} bytes)`);
                            }

                            // Enhanced message validation and handling - EXACT COPY FROM OLD IMPLEMENTATION
                            if (!message || !message.serverContent) {
                                console.warn(`⚠️ [${callSid}] Received invalid message structure`);
                                return;
                            }

                            // Update metrics with null check
                            const metrics = self?.sessionMetrics?.get(callSid);
                            if (metrics) {
                                metrics.messagesReceived = (metrics.messagesReceived || 0) + 1;
                                metrics.lastActivity = Date.now();
                            }

                            // Handle audio response from Gemini with validation - EXACT COPY FROM OLD IMPLEMENTATION
                            const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                            if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                // Enhanced debugging for audio path
                                console.log(`🎵 [${callSid}] Gemini sent AUDIO response:`, {
                                    mimeType: audio.mimeType,
                                    dataLength: audio.data?.length || 0,
                                    hasData: !!audio.data
                                });
                                
                                // Validate audio data before processing
                                if (!audio.data || audio.data.length === 0) {
                                    console.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
                                    return;
                                }

                                // Use new audio forwarding utility for proper handling
                                try {
                                    // Get fresh connection data to ensure we have latest WebSocket state
                                    const freshConnectionData = this.activeConnections?.get(callSid) ||
                                        extConnectionData;
                                    const success = await forwardAudio(
                                        callSid,
                                        audio,
                                        freshConnectionData as ExtendedConnectionData,
                                        this.audioProcessor
                                    );

                                    if (success) {
                                        console.log(`✅ [${callSid}] Audio forwarded successfully`);
                                    } else {
                                        console.warn(`⚠️ [${callSid}] Audio forwarding failed - checking connection state`);
                                        const connData = freshConnectionData as ExtendedConnectionData;
                                        console.warn(`⚠️ [${callSid}] Connection data:`, {
                                            hasWs: !!connData?.ws,
                                            hasLocalWs: !!connData?.localWs,
                                            hasTwilioWs: !!connData?.twilioWs,
                                            sessionType: connData?.sessionType,
                                            isTwilioCall: connData?.isTwilioCall,
                                            streamSid: connData?.streamSid
                                        });
                                    }
                                } catch (audioError) {
                                    console.error(`❌ [${callSid}] Error in audio forwarding:`, audioError);
                                }
                            }

                            // Handle text response for summary collection and conversation logging
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${callSid}] Gemini response: ${text.substring(0, 100)}...`);

                                // Update AI responsiveness tracking - CRITICAL FOR TURN MANAGEMENT
                                extConnectionData.lastAIResponse = Date.now();
                                extConnectionData.responseTimeouts = 0; // Reset timeout counter
                                extConnectionData.connectionQuality = 'good';

                                // Update metrics
                                if (metrics) {
                                    metrics.lastActivity = Date.now();
                                }

                                // Log conversation for recovery purposes - CRITICAL FOR CONTEXT PRESERVATION
                                if (extConnectionData.conversationLog) {
                                    // Implement bounded array to prevent memory leaks
                                    const MAX_CONVERSATION_LOG_SIZE = 500; // Limit conversation log entries
                                    
                                    extConnectionData.conversationLog.push({
                                        role: 'assistant',
                                        content: text,
                                        timestamp: Date.now(),
                                        messageId: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
                                    });
                                    
                                    // Remove oldest entries if limit exceeded
                                    if (extConnectionData.conversationLog.length > MAX_CONVERSATION_LOG_SIZE) {
                                        const removed = extConnectionData.conversationLog.splice(0, extConnectionData.conversationLog.length - MAX_CONVERSATION_LOG_SIZE);
                                        console.log(`🧹 [${callSid}] Trimmed ${removed.length} old conversation entries`);
                                    }
                                }

                                // Handle summary collection if requested
                                if (extConnectionData.summaryRequested) {
                                    extConnectionData.summaryText = (extConnectionData.summaryText || '') + text;
                                }
                            }

                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing Gemini message:`, error);
                        }
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            // REMOVED: AI instruction sending from session manager to prevent double instruction sending
            // Instructions will be sent by the appropriate flow handler (local-testing-handler.js or twilio-flow-handler.js)
            // This prevents race conditions and early session termination
            console.log(`📝 [${callSid}] Session created - instructions will be sent by flow handler to prevent double sending`);
            if (config.aiInstructions) {
                console.log(`📝 [${callSid}] AI instructions available (${config.aiInstructions.length} chars) - will be sent by handler`);
            } else {
                console.warn(`⚠️ [${callSid}] No AI instructions available - handler should provide instructions`);
            }

            // Store the session in connectionData immediately after creation
            // This prevents race conditions where callbacks might execute before assignment
            extConnectionData.geminiSession = geminiSession as GeminiSession;

            // Mark session as ready for audio processing
            extConnectionData.sessionReady = true;
            extConnectionData.sessionInitialized = Date.now();

            // Initialize audio forwarding for this session
            initializeAudioForwarding(callSid, extConnectionData, config.sessionType || 'unknown');

            // Verify session was created successfully
            if (!geminiSession) {
                throw new Error('Gemini session creation returned null/undefined');
            }

            console.log(`✅ [${callSid}] Gemini session created successfully and ready for audio`);

            // Add session health check after 5 seconds using timer manager
            timerManager.setTimeout(`${callSid}_health_check`, () => {
                if (extConnectionData.isSessionActive) {
                    console.log(`✅ [${callSid}] Session health check: still active after 5 seconds`);
                } else {
                    console.error(`❌ [${callSid}] Session health check: died within 5 seconds - check logs above for errors`);
                }
            }, 5000);

            return geminiSession as GeminiSession;

        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            console.error(`❌ [${callSid}] Failed to create Gemini session:`, error);
            console.error(`❌ [${callSid}] Error details:`, {
                message: errorMessage,
                code: (error as any)?.code,
                details: (error as any)?.details,
                stack: errorStack?.split('\n').slice(0, 5).join('\n') // First 5 lines of stack
            });

            // Log configuration that failed
            console.error(`❌ [${callSid}] Failed configuration:`, {
                model: config.model,
                voice: config.voice,
                hasInstructions: !!config.aiInstructions,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall
            });

            // Mark session as failed
            extConnectionData.isSessionActive = false;
            extConnectionData.geminiSessionError = errorMessage;

            return null;
        }
    }

    // Note: Message handling is now done directly in the onmessage callback (like old implementation)

    // REMOVED: sendInitialMessage method - now using Live API setup configuration
    // Initial instructions are sent via setup message in session creation, not as client content

    // Legacy method for backward compatibility - Live API handles continuous conversation automatically
    async sendTextToGemini(sessionId: string, geminiSession: GeminiSession, text: string): Promise<void> {
        console.log(`⚠️ [${sessionId}] sendTextToGemini called but not needed in Live API - text: ${text.substring(0, 100)}...`);
        // In Live API, text should be sent as audio through sendRealtimeInput
        // This is a no-op for backward compatibility
    }

    // Legacy method for backward compatibility - Live API handles turn management automatically
    async sendTurnComplete(sessionId: string, geminiSession: GeminiSession): Promise<void> {
        console.log(`⚠️ [${sessionId}] sendTurnComplete called but not needed in Live API`);
        // Voice Activity Detection (VAD) in the Live API automatically manages conversation turns
        // This is a no-op for backward compatibility
    }

    // Send audio to Gemini session (for Twilio calls with μ-law audio)
    async sendAudioToGemini(callSid: string, geminiSession: GeminiSession, audioBuffer: Buffer): Promise<void> {
        try {
            console.log(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);

            if (!geminiSession || !audioBuffer) {
                console.log(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
                return;
            }

            // Check if session is ready for audio processing
            const connectionData = this.activeConnections?.get(callSid) as ExtendedConnectionData;
            if (!connectionData?.sessionReady) {
                console.log(`⚠️ [${callSid}] Session not ready for audio processing yet`);
                return;
            }

            console.log(`🔍 [${callSid}] Updating metrics...`);
            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            console.log(`🔍 [${callSid}] Converting audio format - audioProcessor exists: ${!!this.audioProcessor}`);
            // Convert Twilio audio to Gemini format
            const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioBuffer);
            console.log(`🔍 [${callSid}] PCM conversion complete - buffer size: ${pcmBuffer.length}`);

            const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer);
            console.log(`🔍 [${callSid}] Float32 conversion complete - array length: ${float32Data.length}`);

            const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);
            console.log(`🔍 [${callSid}] Audio blob created - size: ${audioBlob.data?.length || 'N/A'}`);

            console.log(`🔍 [${callSid}] Sending audio to Gemini session...`);
            // Send to Gemini using sendRealtimeInput (like old implementation)
            await geminiSession.sendRealtimeInput({
                media: {
                    mimeType: audioBlob.mimeType,
                    data: audioBlob.data
                }
            });
            console.log(`✅ [${callSid}] Audio sent to Gemini successfully`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending audio to Gemini:`, error);
        }
    }

    // Send browser PCM audio to Gemini session (for local testing)
    async sendBrowserAudioToGemini(callSid: string, geminiSession: GeminiSession, base64Audio: string): Promise<void> {
        try {
            console.log(`🔍 [${callSid}] sendBrowserAudioToGemini called - geminiSession: ${!!geminiSession}, audioSize: ${base64Audio?.length || 0}`);

            if (!geminiSession || !base64Audio) {
                console.log(`⚠️ [${callSid}] sendBrowserAudioToGemini early return - missing geminiSession or audio`);
                return;
            }

            // Check if session is ready for audio processing
            const connectionData = this.activeConnections?.get(callSid) as ExtendedConnectionData;
            if (!connectionData?.sessionReady) {
                console.log(`⚠️ [${callSid}] Session not ready for audio processing yet`);
                return;
            }

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            // MATCH OLD IMPLEMENTATION: Send audio exactly as received
            console.log(`🎵 [${callSid}] Sending browser audio to Gemini (matching old implementation)...`);
            
            try {
                // Send audio EXACTLY like old implementation - just pass the media object
                await geminiSession.sendRealtimeInput({
                    media: {
                        data: base64Audio,
                        mimeType: 'audio/pcm;rate=16000'  // Browser sends PCM16
                    }
                });
                console.log(`✅ [${callSid}] Browser audio sent to Gemini successfully`);
                
                // Add activity timestamp to track continuous flow
                const currentTime = Date.now();
                console.log(`⏱️ [${callSid}] Audio sent at: ${currentTime}, session active: ${!!geminiSession}`);
                
            } catch (sendError: unknown) {
                console.error(`🚨 [${callSid}] GEMINI AUDIO SEND ERROR 🚨`);
                console.error(`❌ [${callSid}] Error sending browser audio directly:`, sendError);
                const errorMessage = sendError instanceof Error ? sendError.message : 'No message';
                console.error(`❌ [${callSid}] Error message: ${errorMessage}`);

                // Check for quota errors
                if (errorMessage.includes('quota') || errorMessage.includes('exceeded')) {
                    console.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN AUDIO SEND 🚨🚨🚨`);
                    console.error(`💳 [${callSid}] Check your Gemini API billing and quota limits!`);
                }
                
                // Try with WebM mime type as fallback
                try {
                    await geminiSession.sendRealtimeInput({
                        media: {
                            data: base64Audio,
                            mimeType: 'audio/webm'
                        }
                    });
                    console.log(`⚠️ [${callSid}] Browser audio sent with WebM mime type (fallback)`);
                } catch (fallbackError: unknown) {
                    console.error(`🚨 [${callSid}] FALLBACK AUDIO SEND ALSO FAILED 🚨`);
                    console.error(`❌ [${callSid}] All audio sending methods failed:`, fallbackError);
                    const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : 'No message';
                    console.error(`❌ [${callSid}] Fallback error message: ${fallbackErrorMessage}`);

                    if (fallbackErrorMessage.includes('quota') || fallbackErrorMessage.includes('exceeded')) {
                        console.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN FALLBACK TOO 🚨🚨🚨`);
                    }
                }
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending browser audio to Gemini:`, error);
        }
    }

    // Recover session after interruption
    async recoverSession(callSid: string, reason: string): Promise<void> {
        // Atomic check and set to prevent concurrent recovery attempts
        const wasAlreadyRecovering = this.recoveryInProgress.has(callSid);
        if (wasAlreadyRecovering) {
            console.log(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }
        
        // Immediately add to recovery set before any async operations
        this.recoveryInProgress.add(callSid);
        
        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            // Get connection data from active connections (would need to be passed in)
            // This is a simplified version - in practice you'd need access to activeConnections
            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            console.log(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.recoveryCount = (metrics.recoveryCount || 0) + 1;
                metrics.lastRecoveryTime = Date.now();
            }

            // The actual recovery would happen in the main connection handler
            // This method primarily handles the recovery logic and context preparation
            
            console.log(`✅ [${callSid}] Recovery preparation completed`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Generate session summary
    async generateSummary(callSid: string, connectionData: ExtendedConnectionData, summaryPrompt: string): Promise<boolean> {
        try {
            console.log(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData?.geminiSession) {
                console.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return false;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // ALTERNATIVE APPROACH: For Live API, we can't easily get text-only summaries
            // Instead, we'll generate a summary from the conversation log we've been tracking
            // This is more reliable and doesn't interfere with the continuous conversation

            console.log(`📝 [${callSid}] Generating summary from conversation log instead of requesting from AI`);

            // Generate summary from conversation log
            const conversationLog = connectionData.conversationLog || [];
            if (conversationLog.length > 0) {
                const summaryText = this.generateLocalSummary(conversationLog, summaryPrompt);
                connectionData.summaryText = summaryText;
                console.log(`✅ [${callSid}] Local summary generated: ${summaryText.substring(0, 100)}...`);
            } else {
                connectionData.summaryText = 'No conversation content available for summary.';
                console.log(`⚠️ [${callSid}] No conversation log available for summary`);
            }

            // Summary will be collected in the onmessage callback
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error generating summary:`, error);
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid: string): SessionMetrics | null {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    cleanupSession(callSid: string): void {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        // Clear all timers for this session
        timerManager.clearSessionTimers(callSid);
        console.log(`🧹 [${callSid}] Session manager cleanup completed`);
    }

    // Generate a local summary from conversation log (for Live API compatibility)
    private generateLocalSummary(conversationLog: ConversationEntry[], summaryPrompt: string): string {
        try {
            if (!conversationLog || conversationLog.length === 0) {
                return 'No conversation content available.';
            }

            // Extract key information from conversation log
            const totalMessages = conversationLog.length;
            const userMessages = conversationLog.filter(msg => msg.role === 'user').length;
            const aiMessages = conversationLog.filter(msg => msg.role === 'assistant').length;

            // Get first and last few messages for context
            const firstMessages = conversationLog.slice(0, 3).map(msg =>
                `${msg.role}: ${msg.content?.substring(0, 100) || 'Audio message'}`
            ).join('\n');

            const lastMessages = conversationLog.slice(-3).map(msg =>
                `${msg.role}: ${msg.content?.substring(0, 100) || 'Audio message'}`
            ).join('\n');

            const summary = `Call Summary:
- Total messages: ${totalMessages} (${userMessages} from user, ${aiMessages} from AI)
- Duration: ${Math.round((Date.now() - (conversationLog[0]?.timestamp || Date.now())) / 1000)} seconds
- First exchanges:\n${firstMessages}
- Final exchanges:\n${lastMessages}
- Status: Conversation completed`;

            return summary;
        } catch (error) {
            console.error('Error generating local summary:', error);
            return 'Error generating summary from conversation log.';
        }
    }
}