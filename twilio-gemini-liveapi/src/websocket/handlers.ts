import { TranscriptionManager } from '../audio/transcription-manager';
import { handleTwilioFlow } from './twilio-flow-handler';
import { handleLocalTestingFlow } from './local-testing-handler';
import {
    getOutboundCallConfig,
    getInboundCallConfig,
    getOutboundTestConfig,
    getInboundTestConfig
} from './config-handlers';
import type { WebSocketConnection, WebSocketDependencies, FlowDependencies } from '../types/websocket';
import type { FastifyInstance } from 'fastify';
import { authLogger } from '../utils/logger';
import { timingSafeEqual } from 'crypto';

/**
 * Validate WebSocket connection authentication
 * @param req - The WebSocket request object
 * @returns boolean indicating if the connection is authenticated
 */
function validateWebSocketAuth(req: any): boolean {
    // Skip auth for Twilio webhooks (they use signature validation)
    const isTwilioWebhook = req.headers['user-agent']?.includes('TwilioProxy') ||
                           req.headers['x-twilio-signature'];

    if (isTwilioWebhook) {
        authLogger.info('WebSocket: Allowing Twilio webhook connection');
        return true;
    }

    // For non-Twilio connections, require authentication in production
    if (process.env.NODE_ENV === 'production') {
        const authHeader = req.headers.authorization;

        if (!authHeader) {
            authLogger.error('WebSocket: No authorization header in production', {
                url: req.url,
                ip: req.socket?.remoteAddress
            });
            return false;
        }

        try {
            const token = authHeader.replace('Bearer ', '');

            if (!token || token === 'undefined' || token === 'null') {
                authLogger.error('WebSocket: Invalid token format');
                return false;
            }

            // Validate token format
            if (token.length < 32) {
                authLogger.error('WebSocket: Token too short');
                return false;
            }

            // Check against configured API key
            const validApiKey = process.env.API_KEY || process.env.SUPABASE_SERVICE_KEY;
            if (!validApiKey) {
                authLogger.error('WebSocket: No API key configured for production');
                return false;
            }

            // Ensure both tokens are the same length to prevent timing attacks
            if (token.length !== validApiKey.length) {
                authLogger.error('WebSocket: Invalid API key length');
                return false;
            }

            // Use timing-safe comparison
            if (!timingSafeEqual(Buffer.from(token), Buffer.from(validApiKey))) {
                authLogger.error('WebSocket: Invalid API key');
                return false;
            }

            authLogger.info('WebSocket: Authentication successful');
            return true;

        } catch (error) {
            authLogger.error('WebSocket: Auth validation failed', error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }

    // Allow in development and test environments with warning
    if (process.env.NODE_ENV === 'test') {
        authLogger.debug('WebSocket: No authentication required for test environment');
    } else {
        authLogger.warn('WebSocket: No authentication required for development environment');
    }

    return true;
}

// WebSocket handlers for different connection types
export function registerWebSocketHandlers(fastify: FastifyInstance, dependencies: WebSocketDependencies): void {
    // Initialize global transcription manager
    const transcriptionManager = new TranscriptionManager();

    // Add transcription manager to dependencies for consistency
    const enhancedDependencies: WebSocketDependencies = {
        ...dependencies,
        transcriptionManager
    };

    // 1. OUTBOUND CALLS - Twilio media stream for outbound calls
    fastify.register(async (fastify) => {
        console.log('📡 Registering /media-stream WebSocket route for outbound calls');
        fastify.get('/media-stream', { websocket: true }, (connection: any, req: any) => {
            console.log('🔌 WebSocket connection received on /media-stream');
            console.log('🔌 WebSocket headers:', req.headers);

            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                authLogger.error('WebSocket: Authentication failed for /media-stream');
                connection.socket.close(1008, 'Authentication required');
                return;
            }

            handleOutboundCall(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: false
            } as FlowDependencies);
        });
    });

    // 2. INBOUND CALLS - Twilio media stream for inbound calls
    fastify.register(async (fastify) => {
        console.log('📡 Registering /media-stream-inbound WebSocket route for inbound calls');
        fastify.get('/media-stream-inbound', { websocket: true }, (connection: any, req: any) => {
            console.log('🔌 WebSocket connection received on /media-stream-inbound');
            console.log('🔌 WebSocket headers:', req.headers);

            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                authLogger.error('WebSocket: Authentication failed for /media-stream-inbound');
                connection.socket.close(1008, 'Authentication required');
                return;
            }

            handleInboundCall(connection, {
                ...enhancedDependencies,
                callType: 'inbound',
                isTestMode: false
            } as FlowDependencies);
        });
    });

    // 3. OUTBOUND TESTING MODE - Local audio testing for outbound scripts
    fastify.register(async (fastify) => {
        fastify.get('/test-outbound', { websocket: true }, (connection: any, req: any) => {
            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                authLogger.error('WebSocket: Authentication failed for /test-outbound');
                connection.socket.close(1008, 'Authentication required');
                return;
            }

            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // 4. INBOUND TESTING MODE - Local audio testing for inbound scripts
    fastify.register(async (fastify) => {
        fastify.get('/test-inbound', { websocket: true }, (connection: any, req: any) => {
            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                authLogger.error('WebSocket: Authentication failed for /test-inbound');
                connection.socket.close(1008, 'Authentication required');
                return;
            }

            handleInboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'inbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // Local audio session endpoint for browser-based testing
    fastify.register(async (fastify) => {
        fastify.get('/local-audio-session', { websocket: true }, (connection: any, req: any) => {
            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                authLogger.error('WebSocket: Authentication failed for /local-audio-session');
                connection.socket.close(1008, 'Authentication required');
                return;
            }

            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // Direct Gemini Live API endpoint
    fastify.register(async (fastify) => {
        fastify.get('/gemini-live', { websocket: true }, (connection: any, req: any) => {
            console.log('🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection');

            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                authLogger.error('WebSocket: Authentication failed for /gemini-live');
                connection.socket.close(1008, 'Authentication required');
                return;
            }

            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });
}

// 1. OUTBOUND CALLS - Handle real outbound calls via Twilio
function handleOutboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { isTestMode } = deps;
    console.log(`📞 [OUTBOUND] Client connected for outbound call (test: ${isTestMode})`);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: 'outbound_call',
        getSessionConfig: () => getOutboundCallConfig(deps),
        isIncomingCall: false
    });
}

// 2. INBOUND CALLS - Handle real inbound calls via Twilio
function handleInboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { isTestMode } = deps;
    console.log(`📞 [INBOUND] Client connected for inbound call (test: ${isTestMode})`);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: 'inbound_call',
        getSessionConfig: () => getInboundCallConfig(deps),
        isIncomingCall: true
    });
}

// 3. OUTBOUND TESTING MODE - Test outbound scripts locally
function handleOutboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        console.log(`🧪 [OUTBOUND TEST] Client connected for outbound testing`);
        console.log(`🚨 [OUTBOUND TEST] Connection object:`, !!connection);
        console.log(`🚨 [OUTBOUND TEST] Connection socket:`, !!connection.socket);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'outbound_test',
        getSessionConfig: () => getOutboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: false
    });
}

// 4. INBOUND TESTING MODE - Test inbound scripts locally
function handleInboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        console.log(`🧪 [INBOUND TEST] Client connected for inbound testing`);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'inbound_test',
        getSessionConfig: () => getInboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: true
    });
}