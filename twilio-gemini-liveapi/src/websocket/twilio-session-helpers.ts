// Helper functions for Twilio session management to reduce complexity
import { ConnectionData } from '../types/global';
import { FlowDependencies } from '../types/websocket';
import { websocketLogger } from '../utils/logger';

export interface TwilioStreamInfo {
    streamSid?: string;
    accountSid?: string;
    twilioCallSid?: string;
}

export interface SessionConfigResult {
    config: any;
    isValid: boolean;
}

/**
 * Extracts Twilio stream information from start message
 */
export function extractTwilioStreamInfo(data: any): TwilioStreamInfo {
    return {
        streamSid: data.start?.streamSid,
        accountSid: data.start?.accountSid,
        twilioCallSid: data.start?.callSid
    };
}

/**
 * Validates and gets session configuration with fallback
 */
export async function getValidSessionConfig(
    callSid: string,
    getSessionConfig: () => any,
    deps: FlowDependencies,
    isIncomingCall: boolean
): Promise<SessionConfigResult> {
    try {
        let sessionConfig = await getSessionConfig();

        // Ensure we have a valid session config
        if (!sessionConfig || !sessionConfig.aiInstructions) {
            console.warn(`⚠️ [${callSid}] No session config found, using fallback`);
            
            // Try to get current script as fallback
            try {
                const currentScript = isIncomingCall
                    ? deps.scriptManager.getCurrentIncomingScript()
                    : deps.scriptManager.getCurrentOutboundScript();

                if (currentScript) {
                    sessionConfig = deps.scriptManager.getScriptConfig(currentScript, isIncomingCall);
                    console.log(`✅ [${callSid}] Using fallback script: ${currentScript}`);
                }
            } catch (error) {
                console.error(`❌ [${callSid}] Error getting fallback script:`, error);
            }
        }

        return {
            config: sessionConfig,
            isValid: !!(sessionConfig && sessionConfig.aiInstructions)
        };
    } catch (error) {
        console.error(`❌ [${callSid}] Error getting session config:`, error);
        return {
            config: null,
            isValid: false
        };
    }
}

/**
 * Creates enhanced connection data for Twilio sessions
 */
export function createTwilioConnectionData(
    callSid: string,
    ws: any,
    streamInfo: TwilioStreamInfo,
    sessionConfig: any,
    isIncomingCall: boolean,
    flowType: string
): ConnectionData {
    return {
        ws: ws, // Standardized WebSocket property name
        twilioWs: ws, // Also store as twilioWs for audio forwarding compatibility
        callSid,
        sessionId: callSid,
        streamSid: streamInfo.streamSid, // Add streamSid for audio forwarding
        sequenceNumber: 0, // Initialize sequence number for Twilio audio packets
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'twilio_call',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTwilioCall: true,
        // Add missing properties to match testing flows
        lastAIResponse: Date.now(), // Track AI responsiveness
        responseTimeouts: 0, // Count consecutive timeouts
        connectionQuality: 'good', // Track connection quality
        lastContextSave: Date.now(), // For periodic context saving
        contextSaveInterval: null, // For periodic context saving
        // Audio forwarding properties
        audioForwardingEnabled: true, // Enable audio forwarding for Twilio calls
        lastAudioSent: 0 // Track last audio packet sent time
    };
}

/**
 * Logs Twilio stream information
 */
export function logTwilioStreamInfo(
    callSid: string,
    streamInfo: TwilioStreamInfo,
    sessionConfig: any
): void {
    console.log(`🔍 [${callSid}] Twilio stream info:`, {
        streamSid: streamInfo.streamSid,
        accountSid: streamInfo.accountSid,
        twilioCallSid: streamInfo.twilioCallSid,
        configFound: !!sessionConfig,
        hasInstructions: !!sessionConfig?.aiInstructions
    });
}

/**
 * Logs session configuration details
 */
export function logSessionConfiguration(
    callSid: string,
    sessionConfig: any,
    flowType: string
): void {
    console.log(`🔍 [${callSid}] Session config for ${flowType}:`, {
        hasInstructions: !!sessionConfig.aiInstructions,
        instructionsLength: sessionConfig.aiInstructions?.length || 0,
        scriptId: sessionConfig.scriptId,
        scriptType: sessionConfig.scriptType,
        voice: sessionConfig.voice,
        model: sessionConfig.model,
        isIncomingCall: sessionConfig.isIncomingCall
    });
}

/**
 * Starts lifecycle management for a session
 */
export function startLifecycleManagement(
    callSid: string,
    lifecycleManager: any,
    connectionData: ConnectionData,
    sessionConfig: any
): void {
    if (lifecycleManager) {
        lifecycleManager.startSession(callSid, connectionData, sessionConfig);
        console.log(`✅ [${callSid}] Lifecycle management started`);
    }
}

/**
 * Starts WebSocket heartbeat monitoring for Twilio
 */
export function startTwilioHeartbeat(
    callSid: string,
    ws: any,
    globalHeartbeatManager: any,
    activeConnections: Map<string, ConnectionData>
): void {
    // Note: Twilio WebSockets may not respond to standard pings, so use longer timeouts
    globalHeartbeatManager.startHeartbeat(
        callSid,
        ws,
        60000, // 60 second ping interval (longer for Twilio)
        30000, // 30 second pong timeout (longer for Twilio)
        (sessionId: string, ws: any) => {
            websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid });
            // Don't immediately end session on heartbeat timeout for Twilio
            // Mark connection as potentially stale but let call status webhook handle cleanup
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                connectionData.heartbeatTimeout = true;
                connectionData.lastHeartbeatTimeout = Date.now();
                websocketLogger.info('Marked Twilio connection as having heartbeat timeout', { callSid });
            }
        }
    );
}

/**
 * Sends session started message to WebSocket
 */
export function sendSessionStartedMessage(
    ws: any,
    callSid: string,
    flowType: string,
    scriptId: string
): void {
    ws.send(JSON.stringify({
        type: 'session-started',
        callSid,
        flowType,
        scriptId
    }));
}

/**
 * Handles Gemini session creation failure
 */
export async function handleGeminiSessionFailure(
    callSid: string,
    flowType: string,
    ws: any,
    deps: FlowDependencies,
    endSession: (callSid: string, deps: FlowDependencies, reason: string) => void
): Promise<void> {
    websocketLogger.error(`Failed to create Gemini session for ${flowType} call`, { callSid });
    
    ws.send(JSON.stringify({
        type: 'session-error',
        error: 'Failed to initialize AI session. Please try again later.',
        critical: true
    }));
    
    // For Twilio calls, we should end the call gracefully
    if (deps.twilioHelper) {
        try {
            await deps.twilioHelper.endCallWithMessage(
                callSid, 
                'We apologize, but we are unable to process your call at this time. Please try again later.'
            );
        } catch (err) {
            websocketLogger.error(
                'Failed to end call with error message', 
                err instanceof Error ? err : new Error(String(err))
            );
        }
    }

    // Clean up and close connection
    endSession(callSid, deps, 'gemini_session_failed');
    ws.close();
}

/**
 * Handles session start errors
 */
export function handleSessionStartError(
    error: unknown,
    flowType: string,
    ws: any
): void {
    websocketLogger.error(
        `Error starting Twilio ${flowType} session`, 
        error instanceof Error ? error : new Error(String(error))
    );
    
    ws.send(JSON.stringify({
        type: 'session-error',
        error: `Session start failed: ${(error as Error).message}`
    }));
}
